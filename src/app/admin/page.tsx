import { auth0 } from "../../lib/auth0";
import ProtectedRoute from "../../components/ProtectedRoute";
import UserProfile from "../../components/UserProfile";
import { Container, Typography, Box, Button } from "@mui/material";

export default async function AdminPage() {
  const session = await auth0.getSession();

  return (
    <Container maxWidth="lg">
      <Box py={4}>
        <Typography variant="h3" component="h1" gutterBottom align="center">
          Admin Dashboard
        </Typography>
        
        <Typography variant="body1" color="text.secondary" align="center" mb={4}>
          This is a protected admin area. Only authenticated users can access this page.
        </Typography>

        <ProtectedRoute>
          <Box display="flex" flexDirection="column" alignItems="center" gap={4}>
            <Typography variant="h5">
              Welcome to the Admin Area!
            </Typography>
            
            <UserProfile />
            
            <Box textAlign="center">
              <Button variant="outlined" href="/" sx={{ mr: 2 }}>
                Back to Home
              </Button>
              <Button variant="contained" href="/api/auth/logout" color="secondary">
                Logout
              </Button>
            </Box>
          </Box>
        </ProtectedRoute>
      </Box>
    </Container>
  );
}
