import { auth0 } from "../lib/auth0";
import LoginButton from "../components/LoginButton";
import { Container, Typography, Box, Button } from "@mui/material";

export default async function Home() {
  const session = await auth0.getSession();

  return (
    <Container maxWidth="md">
      <Box 
        display="flex" 
        flexDirection="column" 
        alignItems="center" 
        gap={4} 
        py={8}
      >
        <Typography variant="h3" component="h1" gutterBottom>
          Chamber Frontend
        </Typography>
        
        <Typography variant="h6" color="text.secondary" textAlign="center">
          Welcome to the USChamber Team McCooey Project
        </Typography>

        {!session ? (
          <Box textAlign="center">
            <Typography variant="body1" mb={3}>
              Please sign in to access the application
            </Typography>
            <LoginButton />
          </Box>
        ) : (
          <Box textAlign="center">
            <Typography variant="h5" mb={2}>
              Welcome back, {session.user.name}!
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={3}>
              You are successfully authenticated with Auth0
            </Typography>
            
            <Box display="flex" gap={2} justifyContent="center" mb={3}>
              <Button variant="contained" href="/admin">
                Go to Admin Dashboard
              </Button>
              <Button variant="outlined" href="/api/auth/profile">
                View Profile
              </Button>
            </Box>
            
            <LoginButton />
          </Box>
        )}
      </Box>
    </Container>
  );
}
