'use client';

import { useAuth0 } from '@auth0/auth0-react';
import {
  Card,
  CardContent,
  Typography,
  Avatar,
  Box,
  Chip,
  Grid,
  CircularProgress
} from '@mui/material';

export default function UserProfile() {
  const { user, isAuthenticated, isLoading } = useAuth0();

  if (isLoading) return <CircularProgress />;
  if (!isAuthenticated || !user) return <div>No user data available</div>;

  return (
    <Card sx={{ maxWidth: 600, margin: '20px auto' }}>
      <CardContent>
        <Box display="flex" flexDirection="column" alignItems="center" gap={3}>
          <Avatar
            src={user.picture}
            alt={user.name}
            sx={{ width: 100, height: 100 }}
          />
          
          <Typography variant="h4" gutterBottom>
            {user.name}
          </Typography>

          <Grid container spacing={2} sx={{ width: '100%' }}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Email
              </Typography>
              <Typography variant="body1">
                {user.email}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Email Verified
              </Typography>
              <Chip 
                label={user.email_verified ? 'Verified' : 'Not Verified'}
                color={user.email_verified ? 'success' : 'warning'}
                size="small"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                User ID
              </Typography>
              <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
                {user.sub}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Last Updated
              </Typography>
              <Typography variant="body2">
                {user.updated_at ? new Date(user.updated_at).toLocaleDateString() : 'N/A'}
              </Typography>
            </Grid>

            {user.nickname && (
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Nickname
                </Typography>
                <Typography variant="body1">
                  {user.nickname}
                </Typography>
              </Grid>
            )}
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
}
