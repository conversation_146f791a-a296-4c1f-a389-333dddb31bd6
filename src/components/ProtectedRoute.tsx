'use client';

import { useUser } from '@auth0/nextjs-auth0/client';
import { CircularProgress, Box, Typography, Button } from '@mui/material';
import { ReactNode } from 'react';

interface ProtectedRouteProps {
  children: ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { user, error, isLoading } = useUser();

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box display="flex" flexDirection="column" alignItems="center" gap={2} mt={4}>
        <Typography variant="h6" color="error">
          Authentication Error
        </Typography>
        <Typography variant="body2">{error.message}</Typography>
        <Button variant="contained" href="/api/auth/login">
          Try Again
        </Button>
      </Box>
    );
  }

  if (!user) {
    return (
      <Box display="flex" flexDirection="column" alignItems="center" gap={2} mt={4}>
        <Typography variant="h6">Access Denied</Typography>
        <Typography variant="body2">You need to login to access this page.</Typography>
        <Button variant="contained" href="/api/auth/login">
          Login
        </Button>
      </Box>
    );
  }

  return <>{children}</>;
}
