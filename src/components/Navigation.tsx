'use client';

import { useUser } from '@auth0/nextjs-auth0/client';
import { 
  AppBar, 
  Toolbar, 
  Typography, 
  Button, 
  Box,
  Avatar,
  Menu,
  MenuItem,
  IconButton
} from '@mui/material';
import { useState } from 'react';

export default function Navigation() {
  const { user, isLoading } = useUser();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          Chamber Frontend
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button color="inherit" href="/">
            Home
          </Button>
          
          {user && (
            <Button color="inherit" href="/admin">
              Admin
            </Button>
          )}
          
          {!isLoading && (
            <>
              {user ? (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <IconButton
                    size="large"
                    aria-label="account of current user"
                    aria-controls="menu-appbar"
                    aria-haspopup="true"
                    onClick={handleMenu}
                    color="inherit"
                  >
                    <Avatar 
                      src={user.picture} 
                      alt={user.name}
                      sx={{ width: 32, height: 32 }}
                    />
                  </IconButton>
                  <Menu
                    id="menu-appbar"
                    anchorEl={anchorEl}
                    anchorOrigin={{
                      vertical: 'top',
                      horizontal: 'right',
                    }}
                    keepMounted
                    transformOrigin={{
                      vertical: 'top',
                      horizontal: 'right',
                    }}
                    open={Boolean(anchorEl)}
                    onClose={handleClose}
                  >
                    <MenuItem onClick={handleClose}>
                      <Typography variant="body2">
                        {user.name}
                      </Typography>
                    </MenuItem>
                    <MenuItem onClick={handleClose}>
                      <Button 
                        href="/api/auth/profile" 
                        sx={{ textTransform: 'none' }}
                      >
                        Profile
                      </Button>
                    </MenuItem>
                    <MenuItem onClick={handleClose}>
                      <Button 
                        href="/api/auth/logout" 
                        sx={{ textTransform: 'none' }}
                      >
                        Logout
                      </Button>
                    </MenuItem>
                  </Menu>
                </Box>
              ) : (
                <Button color="inherit" href="/api/auth/login">
                  Login
                </Button>
              )}
            </>
          )}
        </Box>
      </Toolbar>
    </AppBar>
  );
}
