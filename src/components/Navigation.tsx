'use client';

import { useAuth0 } from '@auth0/auth0-react';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Avatar,
  Menu,
  MenuItem,
  IconButton
} from '@mui/material';
import { useState } from 'react';

export default function Navigation() {
  const { user, isAuthenticated, isLoading, loginWithRedirect, logout } = useAuth0();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          Chamber Frontend
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button color="inherit" href="/">
            Home
          </Button>
          
          {isAuthenticated && (
            <Button color="inherit" href="/admin">
              Admin
            </Button>
          )}

          {!isLoading && (
            <>
              {isAuthenticated ? (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <IconButton
                    size="large"
                    aria-label="account of current user"
                    aria-controls="menu-appbar"
                    aria-haspopup="true"
                    onClick={handleMenu}
                    color="inherit"
                  >
                    <Avatar
                      src={user?.picture}
                      alt={user?.name}
                      sx={{ width: 32, height: 32 }}
                    />
                  </IconButton>
                  <Menu
                    id="menu-appbar"
                    anchorEl={anchorEl}
                    anchorOrigin={{
                      vertical: 'top',
                      horizontal: 'right',
                    }}
                    keepMounted
                    transformOrigin={{
                      vertical: 'top',
                      horizontal: 'right',
                    }}
                    open={Boolean(anchorEl)}
                    onClose={handleClose}
                  >
                    <MenuItem onClick={handleClose}>
                      <Typography variant="body2">
                        {user?.name}
                      </Typography>
                    </MenuItem>
                    <MenuItem onClick={handleClose}>
                      <Button
                        onClick={() => console.log('User profile:', user)}
                        sx={{ textTransform: 'none' }}
                      >
                        Profile
                      </Button>
                    </MenuItem>
                    <MenuItem onClick={handleClose}>
                      <Button
                        onClick={() => logout({ logoutParams: { returnTo: window.location.origin } })}
                        sx={{ textTransform: 'none' }}
                      >
                        Logout
                      </Button>
                    </MenuItem>
                  </Menu>
                </Box>
              ) : (
                <Button color="inherit" onClick={() => loginWithRedirect()}>
                  Login
                </Button>
              )}
            </>
          )}
        </Box>
      </Toolbar>
    </AppBar>
  );
}
