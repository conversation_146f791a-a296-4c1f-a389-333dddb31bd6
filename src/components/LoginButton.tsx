'use client';

import { useAuth0 } from '@auth0/auth0-react';
import { Button, Card, CardContent, Typography, Box, CircularProgress } from '@mui/material';

export default function LoginButton() {
  const { user, isAuthenticated, isLoading, loginWithRedirect, logout } = useAuth0();

  if (isLoading) return <CircularProgress />;

  if (isAuthenticated && user) {
    return (
      <Card sx={{ maxWidth: 400, margin: '20px auto' }}>
        <CardContent>
          <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
            <Typography variant="h6">Welcome, {user.name}!</Typography>
            <Typography variant="body2" color="text.secondary">
              {user.email}
            </Typography>
            <Button
              variant="contained"
              color="secondary"
              onClick={() => logout({ logoutParams: { returnTo: window.location.origin } })}
            >
              Logout
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ maxWidth: 400, margin: '20px auto' }}>
      <CardContent>
        <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
          <Typography variant="h6">Please Login</Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => loginWithRedirect()}
          >
            Login
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
}
