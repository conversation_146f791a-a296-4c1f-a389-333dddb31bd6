'use client';

import { useUser } from '@auth0/nextjs-auth0/client';
import { Button, Card, CardContent, Typography, Box, CircularProgress } from '@mui/material';

export default function LoginButton() {
  const { user, error, isLoading } = useUser();

  if (isLoading) return <CircularProgress />;
  if (error) return <div>{error.message}</div>;

  if (user) {
    return (
      <Card sx={{ maxWidth: 400, margin: '20px auto' }}>
        <CardContent>
          <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
            <Typography variant="h6">Welcome, {user.name}!</Typography>
            <Typography variant="body2" color="text.secondary">
              {user.email}
            </Typography>
            <Button 
              variant="contained" 
              color="secondary"
              href="/api/auth/logout"
            >
              Logout
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ maxWidth: 400, margin: '20px auto' }}>
      <CardContent>
        <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
          <Typography variant="h6">Please Login</Typography>
          <Button 
            variant="contained" 
            color="primary"
            href="/api/auth/login"
          >
            Login
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
}
