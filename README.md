# Chamber Frontend - USChamber Team McCooey

A Next.js application with Auth0 authentication, Material-UI components, and a modern responsive design.

## Features

- 🔐 **Auth0 Authentication** - Secure login/logout functionality
- 🎨 **Material-UI** - Modern, responsive UI components
- 🛡️ **Protected Routes** - Admin dashboard accessible only to authenticated users
- 👤 **User Profile** - Display user information and authentication status
- ⚡ **Next.js 15** - Latest Next.js with App Router
- 🎯 **TypeScript** - Type-safe development

## Getting Started

### Prerequisites

- Node.js 20 LTS or newer
- An Auth0 account and application

### Installation

1. Clone the repository:
```bash
git clone https://github.com/USChamber/team-mccooey.git
cd chamber-frontend
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
Create a `.env.local` file in the root directory with the following variables:

```env
# Auth0 Configuration
AUTH0_SECRET='your-32-byte-hex-secret'
AUTH0_DOMAIN='your-auth0-domain.auth0.com'
AUTH0_CLIENT_ID='your-auth0-client-id'
AUTH0_CLIENT_SECRET='your-auth0-client-secret'
APP_BASE_URL='http://localhost:3000'
```

4. Generate a secure secret:
```bash
openssl rand -hex 32
```

### Auth0 Setup

1. Create an Auth0 application (Regular Web Application)
2. Configure the following URLs in your Auth0 dashboard:
   - **Allowed Callback URLs**: `http://localhost:3000/api/auth/callback`
   - **Allowed Logout URLs**: `http://localhost:3000`
   - **Allowed Web Origins**: `http://localhost:3000`

### Development

Run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/
│   ├── admin/          # Protected admin dashboard
│   ├── api/auth/       # Auth0 API routes
│   ├── layout.tsx      # Root layout with providers
│   └── page.tsx        # Home page
├── components/
│   ├── LoginButton.tsx # Authentication button
│   ├── ProtectedRoute.tsx # Route protection wrapper
│   └── UserProfile.tsx # User information display
├── lib/
│   └── auth0.ts        # Auth0 client configuration
├── middleware.ts       # Auth0 middleware
└── theme.ts           # Material-UI theme
```

## Authentication Flow

1. Users start on the home page
2. Click "Login" to authenticate via Auth0
3. After successful login, users can access:
   - Admin Dashboard (`/admin`)
   - User Profile information
   - Protected content

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Contributing

1. Create a feature branch
2. Make your changes
3. Test authentication flows
4. Submit a pull request

## Auth0 Routes

The application automatically creates these Auth0 routes:

- `/api/auth/login` - Initiate login
- `/api/auth/logout` - Logout user
- `/api/auth/callback` - Handle Auth0 callback
- `/api/auth/profile` - Get user profile
- `/api/auth/access-token` - Get access token

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `AUTH0_SECRET` | 32-byte hex secret for session encryption | ✅ |
| `AUTH0_DOMAIN` | Your Auth0 domain | ✅ |
| `AUTH0_CLIENT_ID` | Auth0 application client ID | ✅ |
| `AUTH0_CLIENT_SECRET` | Auth0 application client secret | ✅ |
| `APP_BASE_URL` | Application base URL | ✅ |

## Team Information

- **Team**: USChamber/team-mccooey
- **Framework**: Next.js 15 with App Router
- **Authentication**: Auth0
- **UI Library**: Material-UI
- **Language**: TypeScript
